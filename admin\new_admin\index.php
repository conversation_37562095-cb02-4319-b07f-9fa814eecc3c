<?php
// Demo version - No database required
session_start();

// Set demo session data
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_role'] = 'admin';
$_SESSION['is_premium'] = 1;

// Set page title
$page_title = 'ড্যাশবোর্ড';
$current_page = 'index.php';

// Mock statistics for demo
$stats = [
    'movies' => 1247,
    'premium_movies' => 523,
    'tvshows' => 189,
    'premium_tvshows' => 87,
    'episodes' => 3456,
    'premium_episodes' => 1234,
    'users' => 8934,
    'premium_users' => 567,
    'pending_payments' => 23,
    'completed_payments' => 445,
    'payments' => 468,
    'categories' => 15,
    'reviews' => 2341,
    'revenue' => 45678.50
];

// Mock recent activities
$recent_activities = [
    ['user' => 'user123', 'action' => 'নতুন মুভি যোগ করেছে', 'time' => '৫ মিনিট আগে', 'type' => 'success'],
    ['user' => 'admin', 'action' => 'পেমেন্ট অনুমোদন করেছে', 'time' => '১০ মিনিট আগে', 'type' => 'info'],
    ['user' => 'moviefan', 'action' => 'প্রিমিয়াম প্ল্যান কিনেছে', 'time' => '১৫ মিনিট আগে', 'type' => 'warning'],
    ['user' => 'viewer01', 'action' => 'নতুন রিভিউ দিয়েছে', 'time' => '২০ মিনিট আগে', 'type' => 'primary'],
    ['user' => 'premium_user', 'action' => 'প্রোফাইল আপডেট করেছে', 'time' => '২৫ মিনিট আগে', 'type' => 'secondary']
];
?>
<!DOCTYPE html>
<html lang="bn" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - CinePix Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="assets/css/admin-style.css" rel="stylesheet">
    <link href="assets/css/responsive.css" rel="stylesheet">
    
    <style>
        /* Inline styles for demo */
        :root {
            --primary-color: #e50914;
            --secondary-color: #b20710;
            --dark-bg: #0f0f0f;
            --card-bg: #1a1a1a;
            --text-light: #ffffff;
            --text-muted: #b3b3b3;
            --border-color: #333333;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --danger-color: #dc3545;
        }

        body {
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1a1a1a 100%);
            color: var(--text-light);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .topbar {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-bottom: 2px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-light) !important;
        }

        .main-content {
            padding: 2rem;
            margin-left: 0;
        }

        .page-header {
            background: linear-gradient(135deg, var(--card-bg), #2d2d2d);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--primary-color), #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--card-bg), #2d2d2d);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(229, 9, 20, 0.2);
        }

        .stat-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), #ff6b6b, #ffa726);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--text-light), #f1f1f1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        .stat-icon {
            font-size: 2rem;
            color: var(--primary-color);
        }

        .card {
            background: linear-gradient(135deg, var(--card-bg), #2d2d2d);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .card-header {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-bottom: 1px solid var(--border-color);
            border-radius: 15px 15px 0 0;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), #ff4757);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(229, 9, 20, 0.4);
        }

        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            background: rgba(229, 9, 20, 0.1);
        }

        .badge {
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-weight: 600;
        }

        /* Mobile Responsive */
        @media (max-width: 575.98px) {
            .main-content {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 1.8rem;
            }
            
            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body class="admin-body">
    <!-- Topbar -->
    <nav class="navbar topbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-film me-2"></i>
                <span class="text-primary">CINE</span><span class="text-light">PIX</span>
                <span class="ms-2 badge bg-secondary">Admin</span>
            </a>
            
            <div class="d-flex align-items-center">
                <span class="text-light me-3">
                    <i class="fas fa-user-circle me-2"></i>
                    <?php echo $_SESSION['username']; ?>
                </span>
                <button class="btn btn-outline-light btn-sm">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="page-title">
                        <i class="fas fa-tachometer-alt me-3"></i>ড্যাশবোর্ড
                    </h1>
                    <p class="text-muted mb-0">CinePix Admin Panel - নতুন ডিজাইন</p>
                </div>
                <div class="col-auto">
                    <button class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>নতুন কনটেন্ট যোগ করুন
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-film"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="text-end">
                                <div class="stat-number"><?php echo number_format($stats['movies']); ?></div>
                                <div class="stat-label">মোট মুভি</div>
                                <small class="text-warning">
                                    <i class="fas fa-crown me-1"></i><?php echo $stats['premium_movies']; ?> প্রিমিয়াম
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-tv"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="text-end">
                                <div class="stat-number"><?php echo number_format($stats['tvshows']); ?></div>
                                <div class="stat-label">টিভি শো</div>
                                <small class="text-warning">
                                    <i class="fas fa-crown me-1"></i><?php echo $stats['premium_tvshows']; ?> প্রিমিয়াম
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="text-end">
                                <div class="stat-number"><?php echo number_format($stats['users']); ?></div>
                                <div class="stat-label">মোট ইউজার</div>
                                <small class="text-success">
                                    <i class="fas fa-star me-1"></i><?php echo $stats['premium_users']; ?> প্রিমিয়াম
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="text-end">
                                <div class="stat-number">৳<?php echo number_format($stats['revenue']); ?></div>
                                <div class="stat-label">মোট রেভিনিউ</div>
                                <small class="text-info">
                                    <i class="fas fa-chart-line me-1"></i><?php echo $stats['completed_payments']; ?> পেমেন্ট
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Activities -->
        <div class="row">
            <div class="col-xl-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0 text-white">
                            <i class="fas fa-chart-line me-2"></i>ট্রাফিক ওভারভিউ
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="trafficChart" height="100"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0 text-white">
                            <i class="fas fa-clock me-2"></i>সাম্প্রতিক কার্যকলাপ
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div style="max-height: 300px; overflow-y: auto;">
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="activity-item">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <span class="badge bg-<?php echo $activity['type']; ?>">
                                                <i class="fas fa-user"></i>
                                            </span>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold"><?php echo $activity['user']; ?></div>
                                            <div class="text-muted small"><?php echo $activity['action']; ?></div>
                                        </div>
                                        <div class="text-muted small"><?php echo $activity['time']; ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0 text-white">
                            <i class="fas fa-bolt me-2"></i>দ্রুত অ্যাকশন
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <a href="movies.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-film me-2"></i>মুভি ম্যানেজ করুন
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="users.php" class="btn btn-outline-success w-100">
                                    <i class="fas fa-users me-2"></i>ইউজার ম্যানেজ করুন
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="payments.php" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-credit-card me-2"></i>পেমেন্ট দেখুন
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="settings.php" class="btn btn-outline-info w-100">
                                    <i class="fas fa-cog me-2"></i>সেটিংস
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Initialize Chart
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('trafficChart');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 'মে', 'জুন'],
                    datasets: [{
                        label: 'ভিজিটর',
                        data: [1200, 1900, 3000, 5000, 2000, 3000],
                        borderColor: '#e50914',
                        backgroundColor: 'rgba(229, 9, 20, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: '#ffffff' }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: { color: '#b3b3b3' },
                            grid: { color: '#333333' }
                        },
                        x: {
                            ticks: { color: '#b3b3b3' },
                            grid: { color: '#333333' }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
