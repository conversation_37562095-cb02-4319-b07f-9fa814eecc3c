/* ===== Responsive Design for CinePix Admin Panel ===== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .main-content {
        padding: 2.5rem;
    }
    
    .card-columns {
        column-count: 4;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199.98px) {
    :root {
        --sidebar-width: 260px;
    }
    
    .main-content {
        padding: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991.98px) {
    :root {
        --sidebar-width: 240px;
    }
    
    .sidebar {
        transform: translateX(-100%);
        z-index: 1040;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 1.5rem;
    }
    
    .admin-footer {
        margin-left: 0;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .topbar .navbar-brand {
        font-size: 1.25rem;
    }
    
    .search-container {
        display: none !important;
    }
    
    /* Hide some text in navigation */
    .sidebar-nav .nav-text {
        font-size: 0.9rem;
    }
    
    /* Adjust dropdown menus */
    .dropdown-menu {
        min-width: 250px;
    }
    
    /* Table responsive */
    .table-responsive {
        font-size: 0.9rem;
    }
    
    /* Card adjustments */
    .card-body {
        padding: 1.25rem;
    }
    
    /* Form adjustments */
    .form-control,
    .form-select {
        font-size: 0.9rem;
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767.98px) {
    :root {
        --topbar-height: 60px;
        --sidebar-width: 280px;
    }
    
    .topbar {
        height: var(--topbar-height);
        padding: 0.5rem 1rem;
    }
    
    .topbar .navbar-brand {
        font-size: 1.1rem;
    }
    
    .brand-text small {
        display: none;
    }
    
    .sidebar {
        top: var(--topbar-height);
        height: calc(100vh - var(--topbar-height));
        width: var(--sidebar-width);
        transform: translateX(-100%);
    }
    
    .main-content {
        padding: 1rem;
        min-height: calc(100vh - var(--topbar-height));
    }
    
    .admin-container {
        min-height: calc(100vh - var(--topbar-height));
        margin-top: var(--topbar-height);
    }
    
    /* Stats grid for mobile */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-card h3 {
        font-size: 1.5rem;
    }
    
    .stat-card p {
        font-size: 0.8rem;
    }
    
    /* Navigation adjustments */
    .navbar-nav {
        gap: 0.5rem;
    }
    
    .dropdown-menu {
        min-width: 200px;
        font-size: 0.9rem;
    }
    
    /* Sidebar adjustments */
    .sidebar-user-info {
        padding: 0 1rem 1rem;
    }
    
    .user-avatar img {
        width: 40px;
        height: 40px;
    }
    
    .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .nav-icon {
        width: 18px;
        margin-right: 0.5rem;
    }
    
    /* Card adjustments */
    .card {
        margin-bottom: 1rem;
    }
    
    .card-header {
        padding: 1rem;
        font-size: 0.95rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* Button adjustments */
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .btn-group .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
    
    /* Table adjustments */
    .table {
        font-size: 0.8rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
    }
    
    /* Form adjustments */
    .form-control,
    .form-select,
    .form-label {
        font-size: 0.9rem;
    }
    
    .form-control {
        padding: 0.5rem 0.75rem;
    }
    
    /* Modal adjustments */
    .modal-dialog {
        margin: 1rem;
    }
    
    .modal-content {
        font-size: 0.9rem;
    }
    
    /* Hide some elements on mobile */
    .d-mobile-none {
        display: none !important;
    }
    
    /* Show mobile-specific elements */
    .d-mobile-block {
        display: block !important;
    }
    
    .d-mobile-flex {
        display: flex !important;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575.98px) {
    :root {
        --sidebar-width: 100%;
    }
    
    .sidebar {
        width: 100%;
    }
    
    .main-content {
        padding: 0.75rem;
    }
    
    /* Single column layout for stats */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .stat-card {
        padding: 0.75rem;
        text-align: center;
    }
    
    .stat-card h3 {
        font-size: 1.25rem;
    }
    
    /* Topbar adjustments */
    .topbar .container-fluid {
        padding: 0 0.5rem;
    }
    
    .topbar .navbar-brand {
        font-size: 1rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.25rem 0.5rem;
    }
    
    /* Dropdown adjustments */
    .dropdown-menu {
        min-width: calc(100vw - 2rem);
        max-width: calc(100vw - 2rem);
        left: 1rem !important;
        right: 1rem !important;
        transform: none !important;
    }
    
    /* Card adjustments */
    .card-header {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    /* Button adjustments */
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    /* Form adjustments */
    .form-control,
    .form-select {
        font-size: 0.8rem;
        padding: 0.375rem 0.5rem;
    }
    
    .form-label {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
    }
    
    /* Table adjustments */
    .table {
        font-size: 0.75rem;
    }
    
    .table th,
    .table td {
        padding: 0.25rem;
    }
    
    /* Hide table columns on very small screens */
    .table .d-none-xs {
        display: none !important;
    }
    
    /* Modal adjustments */
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 0.75rem;
    }
    
    /* Sidebar footer adjustments */
    .sidebar-footer {
        padding: 1rem;
    }
    
    .info-item {
        font-size: 0.75rem;
    }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
    .sidebar-user-info {
        display: none;
    }
    
    .sidebar-footer {
        display: none;
    }
    
    .nav-link {
        padding: 0.5rem 1rem;
    }
    
    .main-content {
        padding: 1rem;
    }
}

/* Print styles */
@media print {
    .sidebar,
    .topbar,
    .admin-footer,
    .btn,
    .dropdown,
    .modal {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0;
        padding: 0;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
        break-inside: avoid;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #666666;
        --text-secondary: #cccccc;
    }
    
    .card {
        border-width: 2px;
    }
    
    .btn {
        border: 2px solid currentColor;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode preference */
@media (prefers-color-scheme: dark) {
    /* Already using dark theme, but can add specific adjustments here */
}

/* Utility classes for responsive design */
.mobile-only {
    display: none;
}

@media (max-width: 767.98px) {
    .mobile-only {
        display: block;
    }
    
    .desktop-only {
        display: none;
    }
}

.tablet-only {
    display: none;
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .tablet-only {
        display: block;
    }
}

/* Flexbox utilities for responsive layouts */
.flex-mobile-column {
    display: flex;
}

@media (max-width: 767.98px) {
    .flex-mobile-column {
        flex-direction: column;
    }
}

/* Grid utilities */
.grid-responsive {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

@media (max-width: 575.98px) {
    .grid-responsive {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
}
