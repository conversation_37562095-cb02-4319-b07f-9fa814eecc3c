<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set page title
$page_title = 'পেমেন্ট ম্যানেজমেন্ট';
$current_page = 'payments.php';

try {
    // Include configuration and functions
    require_once '../../includes/config.php';
    require_once '../../includes/functions.php';

    // Check if user is logged in and is admin
    if (!isLoggedIn() || !isAdmin()) {
        redirect(SITE_URL . '/login.php');
        exit;
    }
} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}

// Handle payment status update
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_status'])) {
    $payment_id = (int)$_POST['payment_id'];
    $new_status = $_POST['status'];
    
    try {
        if ($new_status === 'completed') {
            // Update payment status and user premium status
            $update_payment = "UPDATE payments SET status = 'completed', updated_at = NOW() WHERE id = ?";
            $stmt = mysqli_prepare($conn, $update_payment);
            mysqli_stmt_bind_param($stmt, 'i', $payment_id);
            
            if (mysqli_stmt_execute($stmt)) {
                // Get payment details
                $payment_query = "SELECT user_id, package_type FROM payments WHERE id = ?";
                $stmt2 = mysqli_prepare($conn, $payment_query);
                mysqli_stmt_bind_param($stmt2, 'i', $payment_id);
                mysqli_stmt_execute($stmt2);
                $payment_data = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt2));
                
                if ($payment_data) {
                    // Update user premium status
                    $duration_map = [
                        'basic' => '1 MONTH',
                        'standard' => '1 MONTH', 
                        'premium' => '1 MONTH'
                    ];
                    
                    $duration = $duration_map[$payment_data['package_type']] ?? '1 MONTH';
                    
                    $update_user = "UPDATE users SET is_premium = 1, premium_expires = DATE_ADD(NOW(), INTERVAL $duration) WHERE id = ?";
                    $stmt3 = mysqli_prepare($conn, $update_user);
                    mysqli_stmt_bind_param($stmt3, 'i', $payment_data['user_id']);
                    mysqli_stmt_execute($stmt3);
                }
                
                $success_message = "পেমেন্ট সফলভাবে অনুমোদন করা হয়েছে।";
            }
        } else {
            // Just update payment status
            $update_payment = "UPDATE payments SET status = ?, updated_at = NOW() WHERE id = ?";
            $stmt = mysqli_prepare($conn, $update_payment);
            mysqli_stmt_bind_param($stmt, 'si', $new_status, $payment_id);
            
            if (mysqli_stmt_execute($stmt)) {
                $success_message = "পেমেন্ট স্ট্যাটাস আপডেট করা হয়েছে।";
            }
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$package_filter = $_GET['package'] ?? '';
$sort_by = $_GET['sort'] ?? 'created_at';
$sort_order = $_GET['order'] ?? 'DESC';

// Build query
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(p.transaction_id LIKE ? OR u.username LIKE ? OR u.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "p.status = ?";
    $params[] = $status_filter;
}

if (!empty($package_filter)) {
    $where_conditions[] = "p.package_type = ?";
    $params[] = $package_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count
$count_query = "SELECT COUNT(*) as total FROM payments p JOIN users u ON p.user_id = u.id $where_clause";
$stmt = mysqli_prepare($conn, $count_query);
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}
mysqli_stmt_execute($stmt);
$total_payments = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt))['total'];

// Pagination
$page = $_GET['page'] ?? 1;
$per_page = 20;
$total_pages = ceil($total_payments / $per_page);
$offset = ($page - 1) * $per_page;

// Get payments
$query = "SELECT p.*, u.username, u.email 
          FROM payments p 
          JOIN users u ON p.user_id = u.id 
          $where_clause 
          ORDER BY p.$sort_by $sort_order 
          LIMIT $per_page OFFSET $offset";

$stmt = mysqli_prepare($conn, $query);
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}
mysqli_stmt_execute($stmt);
$payments_result = mysqli_stmt_get_result($stmt);

// Get payment statistics
$stats_query = "SELECT 
    COUNT(*) as total_payments,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_payments,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
    SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue
    FROM payments";
$stats_result = mysqli_query($conn, $stats_query);
$stats = mysqli_fetch_assoc($stats_result);

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-credit-card me-3"></i>পেমেন্ট ম্যানেজমেন্ট
                </h1>
                <p class="page-subtitle text-muted">মোট <?php echo number_format($total_payments); ?> টি পেমেন্ট</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>রিফ্রেশ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-credit-card"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo number_format($stats['total_payments']); ?></h3>
                                <p class="stat-label">মোট পেমেন্ট</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo number_format($stats['pending_payments']); ?></h3>
                                <p class="stat-label">পেন্ডিং</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number"><?php echo number_format($stats['completed_payments']); ?></h3>
                                <p class="stat-label">সম্পন্ন</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stat-card bg-gradient-info">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number">৳<?php echo number_format($stats['total_revenue']); ?></h3>
                                <p class="stat-label">মোট আয়</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">খুঁজুন</label>
                    <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Transaction ID, ইউজারনেম...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">স্ট্যাটাস</label>
                    <select class="form-select" name="status">
                        <option value="">সব স্ট্যাটাস</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>পেন্ডিং</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>সম্পন্ন</option>
                        <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>ব্যর্থ</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">প্যাকেজ</label>
                    <select class="form-select" name="package">
                        <option value="">সব প্যাকেজ</option>
                        <option value="basic" <?php echo $package_filter === 'basic' ? 'selected' : ''; ?>>বেসিক</option>
                        <option value="standard" <?php echo $package_filter === 'standard' ? 'selected' : ''; ?>>স্ট্যান্ডার্ড</option>
                        <option value="premium" <?php echo $package_filter === 'premium' ? 'selected' : ''; ?>>প্রিমিয়াম</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">সর্ট</label>
                    <select class="form-select" name="sort">
                        <option value="created_at" <?php echo $sort_by == 'created_at' ? 'selected' : ''; ?>>তারিখ</option>
                        <option value="amount" <?php echo $sort_by == 'amount' ? 'selected' : ''; ?>>পরিমাণ</option>
                        <option value="status" <?php echo $sort_by == 'status' ? 'selected' : ''; ?>>স্ট্যাটাস</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <label class="form-label">অর্ডার</label>
                    <select class="form-select" name="order">
                        <option value="DESC" <?php echo $sort_order == 'DESC' ? 'selected' : ''; ?>>নিচে</option>
                        <option value="ASC" <?php echo $sort_order == 'ASC' ? 'selected' : ''; ?>>উপরে</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="payments.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">পেমেন্ট তালিকা</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Transaction ID</th>
                            <th>ইউজার</th>
                            <th>প্যাকেজ</th>
                            <th>পরিমাণ</th>
                            <th>পেমেন্ট মেথড</th>
                            <th>স্ট্যাটাস</th>
                            <th>তারিখ</th>
                            <th width="120">অ্যাকশন</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (mysqli_num_rows($payments_result) > 0): ?>
                            <?php while ($payment = mysqli_fetch_assoc($payments_result)): ?>
                                <tr>
                                    <td>
                                        <div class="fw-bold"><?php echo htmlspecialchars($payment['transaction_id']); ?></div>
                                        <small class="text-muted">ID: <?php echo $payment['id']; ?></small>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?php echo htmlspecialchars($payment['username']); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($payment['email']); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php 
                                            $package_names = [
                                                'basic' => 'বেসিক',
                                                'standard' => 'স্ট্যান্ডার্ড', 
                                                'premium' => 'প্রিমিয়াম'
                                            ];
                                            echo $package_names[$payment['package_type']] ?? $payment['package_type'];
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="fw-bold">৳<?php echo number_format($payment['amount']); ?></div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php 
                                            $method_names = [
                                                'bkash' => 'বিকাশ',
                                                'nagad' => 'নগদ',
                                                'rocket' => 'রকেট'
                                            ];
                                            echo $method_names[$payment['payment_method']] ?? $payment['payment_method'];
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'pending' => 'bg-warning',
                                            'completed' => 'bg-success',
                                            'failed' => 'bg-danger'
                                        ];
                                        $status_names = [
                                            'pending' => 'পেন্ডিং',
                                            'completed' => 'সম্পন্ন',
                                            'failed' => 'ব্যর্থ'
                                        ];
                                        $status_class = $status_classes[$payment['status']] ?? 'bg-secondary';
                                        $status_name = $status_names[$payment['status']] ?? $payment['status'];
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>">
                                            <?php echo $status_name; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small><?php echo date('d/m/Y H:i', strtotime($payment['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <?php if ($payment['status'] === 'pending'): ?>
                                                <button class="btn btn-outline-success" onclick="updatePaymentStatus(<?php echo $payment['id']; ?>, 'completed')" title="অনুমোদন করুন">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="updatePaymentStatus(<?php echo $payment['id']; ?>, 'failed')" title="বাতিল করুন">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php endif; ?>
                                            <a href="view_payment.php?id=<?php echo $payment['id']; ?>" class="btn btn-outline-primary" title="বিস্তারিত দেখুন">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">কোন পেমেন্ট পাওয়া যায়নি।</p>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="card-footer">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Payment Status Update Form -->
<form id="statusUpdateForm" method="POST" style="display: none;">
    <input type="hidden" name="payment_id" id="paymentId">
    <input type="hidden" name="status" id="paymentStatus">
    <input type="hidden" name="update_status" value="1">
</form>

<?php
// Page-specific scripts
$page_scripts = '
<script>
function updatePaymentStatus(paymentId, status) {
    const statusNames = {
        "completed": "অনুমোদন",
        "failed": "বাতিল"
    };
    
    const message = `এই পেমেন্টটি ${statusNames[status]} করতে চান?`;
    
    cinepixAdmin.showConfirmModal(message, function() {
        document.getElementById("paymentId").value = paymentId;
        document.getElementById("paymentStatus").value = status;
        document.getElementById("statusUpdateForm").submit();
    });
}

// Auto refresh every 30 seconds for pending payments
setInterval(function() {
    if (window.location.search.includes("status=pending") || window.location.search === "") {
        location.reload();
    }
}, 30000);
</script>
';

// Include footer
include 'includes/footer.php';
?>
