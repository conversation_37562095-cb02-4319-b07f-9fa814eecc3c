/* ===== Responsive Design for CinePix Admin Panel ===== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .main-content {
        padding: 2.5rem;
    }
    
    .card-columns {
        column-count: 4;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199.98px) {
    :root {
        --sidebar-width: 260px;
    }
    
    .main-content {
        padding: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991.98px) {
    :root {
        --sidebar-width: 240px;
    }
    
    .sidebar {
        transform: translateX(-100%);
        z-index: 1040;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 1.5rem;
    }
    
    .admin-footer {
        margin-left: 0;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .topbar .navbar-brand {
        font-size: 1.25rem;
    }
    
    .search-container {
        display: none !important;
    }
    
    /* Hide some text in navigation */
    .sidebar-nav .nav-text {
        font-size: 0.9rem;
    }
    
    /* Adjust dropdown menus */
    .dropdown-menu {
        min-width: 250px;
    }
    
    /* Table responsive */
    .table-responsive {
        font-size: 0.9rem;
    }
    
    /* Card adjustments */
    .card-body {
        padding: 1.25rem;
    }
    
    /* Form adjustments */
    .form-control,
    .form-select {
        font-size: 0.9rem;
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767.98px) {
    :root {
        --topbar-height: 60px;
        --sidebar-width: 280px;
    }
    
    .topbar {
        height: var(--topbar-height);
        padding: 0.5rem 1rem;
    }
    
    .topbar .navbar-brand {
        font-size: 1.1rem;
    }
    
    .brand-text small {
        display: none;
    }
    
    .sidebar {
        top: var(--topbar-height);
        height: calc(100vh - var(--topbar-height));
        width: var(--sidebar-width);
        transform: translateX(-100%);
    }
    
    .main-content {
        padding: 1rem;
        min-height: calc(100vh - var(--topbar-height));
    }
    
    .admin-container {
        min-height: calc(100vh - var(--topbar-height));
        margin-top: var(--topbar-height);
    }
    
    /* Stats grid for mobile */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-card h3 {
        font-size: 1.5rem;
    }
    
    .stat-card p {
        font-size: 0.8rem;
    }
    
    /* Navigation adjustments */
    .navbar-nav {
        gap: 0.5rem;
    }
    
    .dropdown-menu {
        min-width: 200px;
        font-size: 0.9rem;
    }
    
    /* Sidebar adjustments */
    .sidebar-user-info {
        padding: 0 1rem 1rem;
    }
    
    .user-avatar img {
        width: 40px;
        height: 40px;
    }
    
    .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .nav-icon {
        width: 18px;
        margin-right: 0.5rem;
    }
    
    /* Card adjustments */
    .card {
        margin-bottom: 1rem;
    }
    
    .card-header {
        padding: 1rem;
        font-size: 0.95rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* Button adjustments */
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .btn-group .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
    
    /* Table adjustments */
    .table {
        font-size: 0.8rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
    }
    
    /* Form adjustments */
    .form-control,
    .form-select,
    .form-label {
        font-size: 0.9rem;
    }
    
    .form-control {
        padding: 0.5rem 0.75rem;
    }
    
    /* Modal adjustments */
    .modal-dialog {
        margin: 1rem;
    }
    
    .modal-content {
        font-size: 0.9rem;
    }
    
    /* Hide some elements on mobile */
    .d-mobile-none {
        display: none !important;
    }
    
    /* Show mobile-specific elements */
    .d-mobile-block {
        display: block !important;
    }

    .d-mobile-flex {
        display: flex !important;
    }

    /* Additional mobile optimizations */
    .table th:nth-child(n+6),
    .table td:nth-child(n+6) {
        display: none;
    }

    canvas {
        max-height: 200px !important;
    }

    .activity-item {
        padding: 0.75rem;
    }

    .activity-icon {
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
    }

    .user-item {
        padding: 0.75rem;
    }

    .user-item img {
        width: 24px !important;
        height: 24px !important;
    }

    .bulk-actions {
        padding: 1rem !important;
    }

    .bulk-actions .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .bulk-actions .btn {
        margin-bottom: 0.5rem;
        width: 100%;
    }

    .search-container {
        display: block !important;
        margin: 1rem 0;
    }

    .notification-dropdown,
    .user-dropdown {
        width: 100vw;
        left: 0 !important;
        right: 0 !important;
        transform: none !important;
    }
}

/* Mobile-Specific Design - Completely Different Look for Mobile */
@media (max-width: 575.98px) {
    /* Mobile-First Layout Override */
    body.admin-body {
        background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
    }

    /* Hide desktop sidebar completely on mobile */
    .sidebar {
        display: none !important;
    }

    /* Mobile main content takes full width */
    .main-content {
        margin-left: 0 !important;
        padding: 0.5rem !important;
        width: 100% !important;
    }

    /* Mobile topbar redesign */
    .topbar {
        background: linear-gradient(90deg, #e50914, #b20710) !important;
        border-bottom: 2px solid #333;
        box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
    }

    .topbar .navbar-brand {
        font-size: 1.2rem !important;
        font-weight: 700;
    }

    /* Mobile page header - completely different */
    .page-header {
        background: linear-gradient(135deg, #1a1a1a, #2d2d2d) !important;
        border-radius: 15px !important;
        padding: 1.5rem !important;
        margin-bottom: 1rem !important;
        text-align: center !important;
        border: 1px solid #333 !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    }

    .page-title {
        font-size: 1.3rem !important;
        margin-bottom: 0.5rem !important;
        background: linear-gradient(45deg, #e50914, #ff6b6b) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
    }

    .page-subtitle {
        font-size: 0.8rem !important;
        opacity: 0.8;
    }

    /* Mobile action buttons */
    .page-actions {
        margin-top: 1rem !important;
        justify-content: center !important;
    }

    .page-actions .btn {
        border-radius: 25px !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3) !important;
        border: none !important;
        background: linear-gradient(45deg, #e50914, #ff4757) !important;
        transition: all 0.3s ease !important;
    }

    .page-actions .btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(229, 9, 20, 0.4) !important;
    }

    /* Mobile stats cards - completely different design */
    .stats-grid {
        display: flex !important;
        flex-direction: column !important;
        gap: 1rem !important;
    }

    .stat-card {
        background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%) !important;
        border: 1px solid #333 !important;
        border-radius: 20px !important;
        padding: 1.5rem !important;
        position: relative !important;
        overflow: hidden !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
        transform: none !important;
        transition: all 0.3s ease !important;
    }

    .stat-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 15px 40px rgba(229, 9, 20, 0.2) !important;
    }

    .stat-card::before {
        content: "" !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 4px !important;
        background: linear-gradient(90deg, #e50914, #ff6b6b, #ffa726) !important;
    }

    .stat-card .card-body {
        padding: 0 !important;
        text-align: center !important;
    }

    .stat-card .row {
        flex-direction: column !important;
        align-items: center !important;
    }

    .stat-icon {
        width: 60px !important;
        height: 60px !important;
        margin: 0 auto 1rem !important;
        background: linear-gradient(45deg, #e50914, #ff4757) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 1.5rem !important;
        color: white !important;
        box-shadow: 0 8px 25px rgba(229, 9, 20, 0.3) !important;
    }

    .stat-number {
        font-size: 2.5rem !important;
        font-weight: 800 !important;
        background: linear-gradient(45deg, #fff, #f1f1f1) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        margin-bottom: 0.5rem !important;
    }

    .stat-label {
        font-size: 1rem !important;
        color: #ccc !important;
        margin-bottom: 0.5rem !important;
    }

    .stat-sublabel {
        font-size: 0.8rem !important;
        color: #999 !important;
        background: rgba(229, 9, 20, 0.1) !important;
        padding: 0.25rem 0.75rem !important;
        border-radius: 15px !important;
        display: inline-block !important;
    }

    /* Mobile cards - different design */
    .card {
        background: linear-gradient(135deg, #1a1a1a, #2d2d2d) !important;
        border: 1px solid #333 !important;
        border-radius: 15px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
        margin-bottom: 1.5rem !important;
    }

    .card-header {
        background: linear-gradient(90deg, #e50914, #b20710) !important;
        border-bottom: 1px solid #333 !important;
        border-radius: 15px 15px 0 0 !important;
        padding: 1rem 1.5rem !important;
    }

    .card-title {
        color: white !important;
        font-weight: 600 !important;
        margin-bottom: 0 !important;
    }

    .card-body {
        padding: 1.5rem !important;
    }

    /* Mobile table - card-based design */
    .table-responsive {
        border-radius: 15px !important;
        overflow: hidden !important;
    }

    .table {
        background: transparent !important;
        margin-bottom: 0 !important;
    }

    .table thead {
        display: none !important; /* Hide table headers on mobile */
    }

    .table tbody tr {
        display: block !important;
        background: linear-gradient(135deg, #1a1a1a, #2d2d2d) !important;
        border: 1px solid #333 !important;
        border-radius: 15px !important;
        margin-bottom: 1rem !important;
        padding: 1rem !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
    }

    .table tbody td {
        display: block !important;
        border: none !important;
        padding: 0.5rem 0 !important;
        text-align: left !important;
        position: relative !important;
        padding-left: 40% !important;
    }

    .table tbody td:before {
        content: attr(data-label) !important;
        position: absolute !important;
        left: 0 !important;
        width: 35% !important;
        padding-right: 10px !important;
        white-space: nowrap !important;
        font-weight: 600 !important;
        color: #e50914 !important;
    }

    /* Mobile forms */
    .form-control,
    .form-select {
        background: linear-gradient(135deg, #1a1a1a, #2d2d2d) !important;
        border: 1px solid #333 !important;
        border-radius: 10px !important;
        color: white !important;
        padding: 0.75rem 1rem !important;
        font-size: 1rem !important;
    }

    .form-control:focus,
    .form-select:focus {
        background: linear-gradient(135deg, #2d2d2d, #3a3a3a) !important;
        border-color: #e50914 !important;
        box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25) !important;
    }

    .form-label {
        color: #ccc !important;
        font-weight: 600 !important;
        margin-bottom: 0.5rem !important;
    }

    /* Mobile buttons */
    .btn {
        border-radius: 10px !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
    }

    .btn-primary {
        background: linear-gradient(45deg, #e50914, #ff4757) !important;
        border: none !important;
        box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3) !important;
    }

    .btn-primary:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(229, 9, 20, 0.4) !important;
    }

    .btn-outline-primary {
        border: 2px solid #e50914 !important;
        color: #e50914 !important;
        background: transparent !important;
    }

    .btn-outline-primary:hover {
        background: linear-gradient(45deg, #e50914, #ff4757) !important;
        border-color: #e50914 !important;
        transform: translateY(-2px) !important;
    }

    /* Mobile badges */
    .badge {
        border-radius: 15px !important;
        padding: 0.5rem 1rem !important;
        font-size: 0.8rem !important;
        font-weight: 600 !important;
    }

    /* Mobile pagination */
    .pagination {
        justify-content: center !important;
        flex-wrap: wrap !important;
        gap: 0.5rem !important;
    }

    .page-link {
        border-radius: 10px !important;
        padding: 0.5rem 1rem !important;
        background: linear-gradient(135deg, #1a1a1a, #2d2d2d) !important;
        border: 1px solid #333 !important;
        color: white !important;
        margin: 0 !important;
    }

    .page-link:hover {
        background: linear-gradient(45deg, #e50914, #ff4757) !important;
        border-color: #e50914 !important;
        color: white !important;
        transform: translateY(-2px) !important;
    }

    .page-item.active .page-link {
        background: linear-gradient(45deg, #e50914, #ff4757) !important;
        border-color: #e50914 !important;
    }

    /* Mobile Navigation */
    .mobile-nav-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 1040;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .mobile-nav-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .mobile-nav {
        position: fixed;
        top: 0;
        left: -100%;
        width: 280px;
        height: 100%;
        background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
        z-index: 1050;
        transition: all 0.3s ease;
        overflow-y: auto;
        border-right: 2px solid #333;
    }

    .mobile-nav.show {
        left: 0;
    }

    .mobile-nav-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem 1.5rem;
        background: linear-gradient(90deg, #e50914, #b20710);
        border-bottom: 1px solid #333;
    }

    .mobile-nav-brand {
        display: flex;
        align-items: center;
        font-size: 1.2rem;
        font-weight: 700;
        color: white;
    }

    .mobile-nav-user {
        display: flex;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid #333;
        background: rgba(229, 9, 20, 0.1);
    }

    .mobile-nav-menu {
        padding: 1rem 0;
    }

    .mobile-nav-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        color: #ccc;
        text-decoration: none;
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
    }

    .mobile-nav-item:hover {
        background: rgba(229, 9, 20, 0.1);
        color: white;
        border-left-color: #e50914;
    }

    .mobile-nav-item.active {
        background: linear-gradient(90deg, rgba(229, 9, 20, 0.2), transparent);
        color: #e50914;
        border-left-color: #e50914;
    }

    .mobile-nav-item i {
        width: 20px;
        margin-right: 1rem;
        font-size: 1.1rem;
    }

    .mobile-nav-section {
        margin-bottom: 1rem;
    }

    .mobile-nav-section-title {
        padding: 0.5rem 1.5rem;
        font-size: 0.8rem;
        font-weight: 600;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 1px;
        border-bottom: 1px solid #333;
        margin-bottom: 0.5rem;
    }

    /* Mobile alerts and toasts */
    .alert {
        border-radius: 15px !important;
        border: 1px solid #333 !important;
        background: linear-gradient(135deg, #1a1a1a, #2d2d2d) !important;
    }

    .alert-success {
        border-color: #28a745 !important;
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05)) !important;
    }

    .alert-danger {
        border-color: #dc3545 !important;
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05)) !important;
    }

    .alert-warning {
        border-color: #ffc107 !important;
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)) !important;
    }

    .alert-info {
        border-color: #17a2b8 !important;
        background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05)) !important;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575.98px) {
    :root {
        --sidebar-width: 100%;
    }
    
    .sidebar {
        width: 100%;
    }
    
    .main-content {
        padding: 0.75rem;
    }
    
    /* Single column layout for stats */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .stat-card {
        padding: 0.75rem;
        text-align: center;
    }
    
    .stat-card h3 {
        font-size: 1.25rem;
    }
    
    /* Topbar adjustments */
    .topbar .container-fluid {
        padding: 0 0.5rem;
    }
    
    .topbar .navbar-brand {
        font-size: 1rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.25rem 0.5rem;
    }
    
    /* Dropdown adjustments */
    .dropdown-menu {
        min-width: calc(100vw - 2rem);
        max-width: calc(100vw - 2rem);
        left: 1rem !important;
        right: 1rem !important;
        transform: none !important;
    }
    
    /* Card adjustments */
    .card-header {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    /* Button adjustments */
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    /* Form adjustments */
    .form-control,
    .form-select {
        font-size: 0.8rem;
        padding: 0.375rem 0.5rem;
    }
    
    .form-label {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
    }
    
    /* Table adjustments */
    .table {
        font-size: 0.75rem;
    }
    
    .table th,
    .table td {
        padding: 0.25rem;
    }
    
    /* Hide table columns on very small screens */
    .table .d-none-xs {
        display: none !important;
    }
    
    /* Modal adjustments */
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 0.75rem;
    }
    
    /* Sidebar footer adjustments */
    .sidebar-footer {
        padding: 1rem;
    }
    
    .info-item {
        font-size: 0.75rem;
    }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
    .sidebar-user-info {
        display: none;
    }
    
    .sidebar-footer {
        display: none;
    }
    
    .nav-link {
        padding: 0.5rem 1rem;
    }
    
    .main-content {
        padding: 1rem;
    }
}

/* Print styles */
@media print {
    .sidebar,
    .topbar,
    .admin-footer,
    .btn,
    .dropdown,
    .modal {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0;
        padding: 0;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
        break-inside: avoid;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #666666;
        --text-secondary: #cccccc;
    }
    
    .card {
        border-width: 2px;
    }
    
    .btn {
        border: 2px solid currentColor;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode preference */
@media (prefers-color-scheme: dark) {
    /* Already using dark theme, but can add specific adjustments here */
}

/* Utility classes for responsive design */
.mobile-only {
    display: none;
}

@media (max-width: 767.98px) {
    .mobile-only {
        display: block;
    }
    
    .desktop-only {
        display: none;
    }
}

.tablet-only {
    display: none;
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .tablet-only {
        display: block;
    }
}

/* Flexbox utilities for responsive layouts */
.flex-mobile-column {
    display: flex;
}

@media (max-width: 767.98px) {
    .flex-mobile-column {
        flex-direction: column;
    }
}

/* Grid utilities */
.grid-responsive {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

@media (max-width: 575.98px) {
    .grid-responsive {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
}
