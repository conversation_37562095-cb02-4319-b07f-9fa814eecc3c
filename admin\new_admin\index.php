<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');

// Set page title
$page_title = 'ড্যাশবোর্ড';
$current_page = 'index.php';

try {
    // Include configuration and functions
    require_once '../../includes/config.php';
    require_once '../../includes/functions.php';

    // Check if user is logged in and is admin
    if (!isLoggedIn() || !isAdmin()) {
        redirect(SITE_URL . '/login.php');
        exit;
    }
} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}

// Get statistics
$stats = [
    'movies' => 0,
    'premium_movies' => 0,
    'tvshows' => 0,
    'premium_tvshows' => 0,
    'episodes' => 0,
    'premium_episodes' => 0,
    'users' => 0,
    'premium_users' => 0,
    'pending_payments' => 0,
    'completed_payments' => 0,
    'payments' => 0,
    'categories' => 0,
    'reviews' => 0,
    'revenue' => 0
];

// Get movie statistics
$movie_query = "SELECT 
    COUNT(*) as total_movies,
    COUNT(CASE WHEN is_premium = 1 THEN 1 END) as premium_movies
    FROM movies";
$movie_result = mysqli_query($conn, $movie_query);
if ($movie_result && $movie_row = mysqli_fetch_assoc($movie_result)) {
    $stats['movies'] = $movie_row['total_movies'];
    $stats['premium_movies'] = $movie_row['premium_movies'];
}

// Get TV show statistics
$tvshow_query = "SELECT 
    COUNT(*) as total_tvshows,
    COUNT(CASE WHEN is_premium = 1 THEN 1 END) as premium_tvshows
    FROM tvshows";
$tvshow_result = mysqli_query($conn, $tvshow_query);
if ($tvshow_result && $tvshow_row = mysqli_fetch_assoc($tvshow_result)) {
    $stats['tvshows'] = $tvshow_row['total_tvshows'];
    $stats['premium_tvshows'] = $tvshow_row['premium_tvshows'];
}

// Get episode statistics
$episode_query = "SELECT 
    COUNT(*) as total_episodes,
    COUNT(CASE WHEN is_premium = 1 THEN 1 END) as premium_episodes
    FROM episodes";
$episode_result = mysqli_query($conn, $episode_query);
if ($episode_result && $episode_row = mysqli_fetch_assoc($episode_result)) {
    $stats['episodes'] = $episode_row['total_episodes'];
    $stats['premium_episodes'] = $episode_row['premium_episodes'];
}

// Get user statistics
$user_query = "SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN is_premium = 1 THEN 1 END) as premium_users
    FROM users WHERE role = 'user'";
$user_result = mysqli_query($conn, $user_query);
if ($user_result && $user_row = mysqli_fetch_assoc($user_result)) {
    $stats['users'] = $user_row['total_users'];
    $stats['premium_users'] = $user_row['premium_users'];
}

// Get payment statistics
$payment_query = "SELECT 
    COUNT(*) as total_payments,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_payments,
    SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue
    FROM payments";
$payment_result = mysqli_query($conn, $payment_query);
if ($payment_result && $payment_row = mysqli_fetch_assoc($payment_result)) {
    $stats['payments'] = $payment_row['total_payments'];
    $stats['pending_payments'] = $payment_row['pending_payments'];
    $stats['completed_payments'] = $payment_row['completed_payments'];
    $stats['revenue'] = $payment_row['total_revenue'] ?? 0;
}

// Get category count
$category_query = "SELECT COUNT(*) as total_categories FROM categories";
$category_result = mysqli_query($conn, $category_query);
if ($category_result && $category_row = mysqli_fetch_assoc($category_result)) {
    $stats['categories'] = $category_row['total_categories'];
}

// Get review count
$review_query = "SELECT COUNT(*) as total_reviews FROM reviews";
$review_result = mysqli_query($conn, $review_query);
if ($review_result && $review_row = mysqli_fetch_assoc($review_result)) {
    $stats['reviews'] = $review_row['total_reviews'];
}

// Include header
include 'includes/header.php';
?>

<!-- Include Sidebar -->
<?php include 'includes/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt me-3"></i>ড্যাশবোর্ড
                </h1>
                <p class="page-subtitle text-muted">CinePix Admin Panel এ স্বাগতম</p>
            </div>
            <div class="col-auto">
                <div class="page-actions">
                    <button class="btn btn-outline-primary me-2" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>রিফ্রেশ
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-plus me-2"></i>নতুন যোগ করুন
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="add_movie.php"><i class="fas fa-film me-2"></i>নতুন মুভি</a></li>
                            <li><a class="dropdown-item" href="add_tvshow.php"><i class="fas fa-tv me-2"></i>নতুন টিভি শো</a></li>
                            <li><a class="dropdown-item" href="add_user.php"><i class="fas fa-user-plus me-2"></i>নতুন ইউজার</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row stats-grid mb-4">
        <!-- Movies Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card bg-gradient-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-film"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number" data-stat="movies"><?php echo number_format($stats['movies']); ?></h3>
                                <p class="stat-label">মোট মুভি</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-crown me-1"></i><?php echo $stats['premium_movies']; ?> প্রিমিয়াম
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- TV Shows Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card bg-gradient-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-tv"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number" data-stat="tvshows"><?php echo number_format($stats['tvshows']); ?></h3>
                                <p class="stat-label">টিভি শো</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-crown me-1"></i><?php echo $stats['premium_tvshows']; ?> প্রিমিয়াম
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card bg-gradient-info">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number" data-stat="users"><?php echo number_format($stats['users']); ?></h3>
                                <p class="stat-label">মোট ইউজার</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-crown me-1"></i><?php echo $stats['premium_users']; ?> প্রিমিয়াম
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card bg-gradient-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="stat-content text-end">
                                <h3 class="stat-number">৳<?php echo number_format($stats['revenue']); ?></h3>
                                <p class="stat-label">মোট আয়</p>
                                <small class="stat-sublabel">
                                    <i class="fas fa-clock me-1"></i><?php echo $stats['pending_payments']; ?> পেন্ডিং
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Analytics -->
    <div class="row mb-4">
        <!-- Revenue Chart -->
        <div class="col-xl-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>রেভিনিউ ট্রেন্ড
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-xl-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>দ্রুত পরিসংখ্যান
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-stat-item">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>মোট এপিসোড</span>
                            <span class="badge bg-primary"><?php echo number_format($stats['episodes']); ?></span>
                        </div>
                        <div class="progress mb-3" style="height: 6px;">
                            <div class="progress-bar bg-primary" style="width: 75%"></div>
                        </div>
                    </div>
                    
                    <div class="quick-stat-item">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>ক্যাটাগরি</span>
                            <span class="badge bg-success"><?php echo number_format($stats['categories']); ?></span>
                        </div>
                        <div class="progress mb-3" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: 60%"></div>
                        </div>
                    </div>
                    
                    <div class="quick-stat-item">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>রিভিউ</span>
                            <span class="badge bg-info"><?php echo number_format($stats['reviews']); ?></span>
                        </div>
                        <div class="progress mb-3" style="height: 6px;">
                            <div class="progress-bar bg-info" style="width: 45%"></div>
                        </div>
                    </div>
                    
                    <div class="quick-stat-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>পেমেন্ট</span>
                            <span class="badge bg-warning"><?php echo number_format($stats['payments']); ?></span>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-warning" style="width: 80%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity and Quick Actions -->
    <div class="row">
        <!-- Recent Activity -->
        <div class="col-xl-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>সাম্প্রতিক কার্যকলাপ
                    </h5>
                </div>
                <div class="card-body">
                    <div id="recentActivity">
                        <div class="activity-item">
                            <div class="activity-icon bg-primary">
                                <i class="fas fa-film"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">নতুন মুভি যোগ করা হয়েছে</div>
                                <div class="activity-description">Avengers: Endgame মুভি সফলভাবে যোগ করা হয়েছে</div>
                                <div class="activity-time">৫ মিনিট আগে</div>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon bg-success">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">নতুন ইউজার রেজিস্ট্রেশন</div>
                                <div class="activity-description">john_doe নামে নতুন ইউজার রেজিস্ট্রেশন করেছে</div>
                                <div class="activity-time">১০ মিনিট আগে</div>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon bg-warning">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">নতুন পেমেন্ট</div>
                                <div class="activity-description">৳৯০ প্রিমিয়াম প্ল্যানের জন্য পেমেন্ট পেন্ডিং</div>
                                <div class="activity-time">১৫ মিনিট আগে</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-xl-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>দ্রুত অ্যাকশন
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="add_movie.php" class="btn btn-outline-primary">
                            <i class="fas fa-film me-2"></i>নতুন মুভি যোগ করুন
                        </a>
                        <a href="add_tvshow.php" class="btn btn-outline-success">
                            <i class="fas fa-tv me-2"></i>নতুন টিভি শো যোগ করুন
                        </a>
                        <a href="manage_premium.php" class="btn btn-outline-warning">
                            <i class="fas fa-crown me-2"></i>প্রিমিয়াম ম্যানেজ করুন
                        </a>
                        <a href="payments.php" class="btn btn-outline-info">
                            <i class="fas fa-credit-card me-2"></i>পেমেন্ট দেখুন
                        </a>
                        <a href="backup.php" class="btn btn-outline-secondary">
                            <i class="fas fa-database me-2"></i>ব্যাকআপ নিন
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Page-specific scripts
$page_scripts = '
<script>
// Initialize dashboard charts
document.addEventListener("DOMContentLoaded", function() {
    // Revenue Chart
    const ctx = document.getElementById("revenueChart");
    if (ctx) {
        new Chart(ctx, {
            type: "line",
            data: {
                labels: ["জানুয়ারি", "ফেব্রুয়ারি", "মার্চ", "এপ্রিল", "মে", "জুন"],
                datasets: [{
                    label: "রেভিনিউ (৳)",
                    data: [1200, 1900, 800, 1500, 2000, 2400],
                    borderColor: "#e50914",
                    backgroundColor: "rgba(229, 9, 20, 0.1)",
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: "#ffffff"
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: "#b3b3b3",
                            callback: function(value) {
                                return "৳" + value;
                            }
                        },
                        grid: {
                            color: "#333333"
                        }
                    },
                    x: {
                        ticks: {
                            color: "#b3b3b3"
                        },
                        grid: {
                            color: "#333333"
                        }
                    }
                }
            }
        });
    }
});
</script>
';

// Include footer
include 'includes/footer.php';
?>
